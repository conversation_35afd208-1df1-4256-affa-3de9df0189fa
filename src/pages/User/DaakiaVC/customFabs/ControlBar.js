/* eslint-disable no-unused-vars */
import { Track } from "livekit-client";
import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  MediaDeviceMenu,
  DisconnectButton,
  // TrackToggle,
  // ChatIcon,
  // LeaveIcon,
  // ChatToggle,
  useLocalParticipantPermissions,
  usePersistentUserChoices,
  useMaybeLayoutContext,
} from "@livekit/components-react";
import {
  supportsScreenSharing,
  isMobileBrowser,
} from "@livekit/components-core";
import { Tooltip, Badge, Popover } from "antd";
import { TrackToggle } from "../components/TrackToggle";

import { DrawerState } from "../utils/constants";
import { onDeviceError } from "../utils/helper";
import { useMediaQuery } from "../hooks/useMediaQuerry";
import { mergeProps } from "../utils/mergeProps";
import { StartMediaButton } from "../components/StartMediaButton";
import { SettingsControlButton } from "../components/settings/SettingsControlButton";
import { InfoPopover } from "../components/InfoPopover";
import { ParticipantControlButton } from "../components/participants/ParticipantControlButton";
import { RaiseHandControlButton } from "../components/raisehand/RaiseHandControlButton";
import { ReactionsControlButton } from "../components/reactions/ReactionsControlButton";
import { DisconnectButtonMenu } from "../components/Disconnect/DisconnectButtonMenu";
import { ScreenShareMenuButton } from "../components/ScreenShare/ScreenShareMenuButton";
// import { AnnotationControlButton } from "../components/annotation/AnnotationButton";
import { Timer } from "../components/Timer";
// import { ReactComponent as ChatIcon } from "../assets/icons/ChatIcon.svg";
import { ReactComponent as LeaveIcon } from "../assets/icons/LeaveIcon.svg";
import { ReactComponent as RecordingIconOn } from "../assets/icons/RecordingOnState.svg";
import ChatControlButton from "../components/chats/ChatControlButton";

export function ControlBar({
  variation,
  controls,
  showParticipantsList,
  setShowParticipantsList,
  showHostControl,
  setShowHostControl,
  showBreakoutRoom,
  setShowBreakoutRoom,
  showRaiseHand,
  setShowRaiseHand,
  showEmojiReaction,
  setShowEmojiReaction,
  showRecording,
  widgetUpdate,
  saveUserChoices = true,
  isForceMuteAll,
  isForceVideoOffAll,
  coHostToken,
  drawerState,
  setDrawerState,
  isBreakoutRoom,
  localParticipant,
  remoteParticipants,
  meetingFeatures,
  showChatDrawer,
  setShowChatDrawer,
  privateChatUnreadMessagesCount,
  publicChatUnreadMessagesCount,
  showlivecaptionsicon,
  isWebinarMode,
  isPIPEnabled,
  setIsPIPEnabled,
  sipData,
  isElectronApp,
  screenShareSources,
  isExitWhiteboardModalOpen,
  setIsExitWhiteboardModalOpen,
  whiteboardSceneData,
  setWhiteboardSceneData,
  whiteBoardId,
  setWhiteboardId,
  isAnnotationEnabled,
  setIsAnnotationEnabled,
  setScreenShareDisplayId,
  isScreenShareEnabled,
  setIsScreenShareEnabled,
  screenShareMode,
  setScreenShareMode,
  onScreenShareChange,
  setToastNotification,
  setToastStatus,
  setShowToast,
  deviceIdAudio,
  setDeviceIdAudio,
  deviceIdVideo,
  setDeviceIdVideo,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  isRecordingLoading,
  setIsRecordingLoading,
  setParticipantConsent,
  setShowRecordingConsentDrawer,
  setShowRecordingConsentIcon,
  showRecordingConsentDrawer,
  brightness,
  onBrightnessChange,
  // Volume props (following brightness pattern, no RPC needed)
  outputVolume,
  onOutputVolumeChange,
  // Auto video off props (following volume pattern, no RPC needed)
  autoVideoOff,
  onAutoVideoOffChange,
  // Auto audio off props (following volume pattern, no RPC needed)
  autoAudioOff,
  onAutoAudioOffChange,
  ...props
}) {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [showDisconnectPopover, setShowDisconnectPopover] = useState(false);
  const [hasLoggedHold, setHasLoggedHold] = useState(false);
  const remote = Array.from(remoteParticipants.values());
  const remoteParticipantsArray = [localParticipant, ...remote];

  const layoutContext = useMaybeLayoutContext();

  const { dispatch, state } = layoutContext.widget;

  useEffect(() => {
    if (layoutContext?.widget.state?.showChat !== undefined) {
      setIsChatOpen(layoutContext?.widget.state?.showChat);
    }
    if (layoutContext?.widget.state?.showChat === true) {
      setDrawerState(DrawerState.CHAT);
    }
  }, [layoutContext?.widget.state?.showChat]);

  const shutAllDrawers = useCallback(() => {
    props.setIsRPDrawerOpen(false);
    props.setIsLiveCaptionsDrawerOpen(false);
    props.setIsVBDrawerOpen(false);
    setShowParticipantsList(false);
    setShowHostControl(false);
    setShowBreakoutRoom(false);
    setShowChatDrawer(false);
    setShowRecordingConsentDrawer(false);
  }, [
    props.isLiveCaptionsDrawerOpen,
    props.setIsLiveCaptionsDrawerOpen,
    props.setIsRPDrawerOpen,
    props.setIsVBDrawerOpen,
    setShowParticipantsList,
    props.isRPDrawerOpen,
    props.isVBDrawerOpen,
    showParticipantsList,
    setShowHostControl,
    showHostControl,
    showBreakoutRoom,
    setShowBreakoutRoom,
    showChatDrawer,
    setShowChatDrawer,
    showRecordingConsentDrawer,
  ]);

  useEffect(() => {
    if (
      !props.isLiveCaptionsDrawerOpen &&
      !props.isRPDrawerOpen &&
      !props.isVBDrawerOpen &&
      !showParticipantsList &&
      !showHostControl &&
      !showBreakoutRoom &&
      !isChatOpen &&
      !showChatDrawer &&
      !showRecordingConsentDrawer
    ) {
      setDrawerState(DrawerState.NONE);
    }
  }, [
    props.isRPDrawerOpen,
    props.isLiveCaptionsDrawerOpen,
    props.isVBDrawerOpen,
    showParticipantsList,
    showChatDrawer,
    showRecordingConsentDrawer,
  ]);

  useEffect(() => {
    if (drawerState === DrawerState.CHAT) {
      shutAllDrawers();
      setShowChatDrawer(true);
    } else if (drawerState === DrawerState.PARTICIPANTS) {
      dispatch({ msg: "hide_chat" });
      shutAllDrawers();
      setShowParticipantsList(true);
    } else if (drawerState === DrawerState.VIRTUAL_BACKGROUND) {
      dispatch({ msg: "hide_chat" });
      shutAllDrawers();
      props.setIsVBDrawerOpen(true);
    } else if (drawerState === DrawerState.REPORT_ISSUE) {
      dispatch({ msg: "hide_chat" });
      shutAllDrawers();
      props.setIsRPDrawerOpen(true);
    } else if (drawerState === DrawerState.LIVECAPTION) {
      dispatch({ msg: "hide_chat" });
      shutAllDrawers();
      props.setIsLiveCaptionsDrawerOpen(true);
    } else if (drawerState === DrawerState.NONE) {
      shutAllDrawers();
      dispatch({ msg: "hide_chat" });
    } else if (drawerState === DrawerState.HOSTCONTROL) {
      dispatch({ msg: "hide_chat" });
      shutAllDrawers();
      setShowHostControl(true);
    } else if (drawerState === DrawerState.BREAKOUTROOM) {
      dispatch({ msg: "hide_chat" });
      shutAllDrawers();
      setShowBreakoutRoom(true);
    } else if (drawerState === DrawerState.RECORDING_CONSENT) {
      shutAllDrawers();
      setShowRecordingConsentDrawer(true);
    }
  }, [drawerState]);

  const isTooLittleSpace = useMediaQuery(
    `(max-width: ${isChatOpen ? 1000 : 760}px)`
  );

  const defaultVariation = "minimal";
  variation = variation || defaultVariation;

  const visibleControls = { leave: true, ...controls };

  const localPermissions = useLocalParticipantPermissions();

  if (!localPermissions) {
    visibleControls.camera = false;
    visibleControls.chat = false;
    visibleControls.microphone = false;
    visibleControls.screenShare = false;
  } else {
    visibleControls.camera =
      visibleControls.camera ?? localPermissions.canPublish;
    visibleControls.microphone =
      visibleControls.microphone ?? localPermissions.canPublish;
    visibleControls.screenShare =
      visibleControls.screenShare ?? localPermissions.canPublish;
    visibleControls.chat =
      visibleControls.chat ??
      (localPermissions.canPublishData && controls?.chat);
  }

  const showIcon = useMemo(
    () => variation === "minimal" || variation === "verbose",
    [variation]
  );
  const showText = useMemo(
    () => variation === "textOnly" || variation === "verbose",
    [variation]
  );

  const browserSupportsScreenSharing = supportsScreenSharing();

  const htmlProps = mergeProps({ className: "lk-control-bar" }, props);

  const {
    saveAudioInputEnabled,
    saveVideoInputEnabled,
    saveAudioInputDeviceId,
    saveVideoInputDeviceId,
  } = usePersistentUserChoices({ preventSave: !saveUserChoices });

  const microphoneOnChange = useCallback(
    (enabled, isUserInitiated) =>
      isUserInitiated ? saveAudioInputEnabled(enabled) : null,
    [saveAudioInputEnabled]
  );

  const cameraOnChange = useCallback(
    (enabled, isUserInitiated) =>
      isUserInitiated ? saveVideoInputEnabled(enabled) : null,
    [saveVideoInputEnabled]
  );

  const [controlAudioId, setControlAudioId] = useState("");
  const [controlVideoId, setControlVideoId] = useState("");

  useEffect(() => {
    setControlAudioId(deviceIdAudio);
    setControlVideoId(deviceIdVideo);
  }, [deviceIdAudio, deviceIdVideo]);

  // Add key press event handler
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.code === 'Space' && !hasLoggedHold) {
        localParticipant.setMicrophoneEnabled(true);
        setHasLoggedHold(true);
      }
    };

    const handleKeyUp = (event) => {
      if (event.code === 'Space') {
        localParticipant.setMicrophoneEnabled(false);
        setHasLoggedHold(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    // Cleanup event listeners
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [hasLoggedHold]);

  return (
    <div {...htmlProps} className="lk-control-bar control-bar-container">
      <div
        style={{
          display: "flex",
          alignItems: "center",
        }}
      >
        <Tooltip placement="topLeft" title="Recording in progress">
          {!isMobileBrowser() && showRecording && (
            <RecordingIconOn
              style={{
                position: "absolute",
                left: "20px",
              }}
              className="recording-icon"
            />
          )}
        </Tooltip>

        <div
          style={{
            display: "flex",
            alignItems: "center",
          }}
        >
          {!isMobileBrowser() ? (
            <>
              <div
                className={
                  showRecording
                    ? "controlbar-timer-recording-on"
                    : "controlbar-timer"
                }
                style={{ cursor: "pointer" }}
              >
                <Timer meetingDetails={props.meetingDetails} />
              </div>
              {visibleControls.info && (props.isHost || props.isCoHost) && (
                <InfoPopover
                  id={props.id}
                  meetingDetails={props.meetingDetails}
                  sipData={sipData}
                  meetingFeatures={meetingFeatures}
                  setToastNotification={setToastNotification}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                />
              )}
            </>
          ) : null}
        </div>
      </div>

      {visibleControls.microphone && (
        <Popover
          content={"Microphone"}
          // trigger={"click"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <div
            className={`lk-button-group ${
              isForceMuteAll && (!props.isCoHost || !props.isHost)
                ? "disabled"
                : ""
            }`}
          >
            <TrackToggle
              source={Track.Source.Microphone}
              showIcon={showIcon}
              onChange={microphoneOnChange}
              className="control-bar-button control-bar-button-icon"
              onDeviceError={onDeviceError}
            >
              {showText && "Microphone"}
            </TrackToggle>
            <div className="lk-button-group-menu">
              <MediaDeviceMenu
                kind="audioinput"
                onActiveDeviceChange={(_kind, deviceId) => {
                  saveAudioInputDeviceId(deviceId ?? "");
                  setDeviceIdAudio(deviceId ?? "");
                }}
                initialSelection={controlAudioId}
              />
            </div>
          </div>
        </Popover>
      )}
      {visibleControls.camera && (
        <Popover
          content={"Camera"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <div
            className={`lk-button-group ${
              isForceVideoOffAll && (!props.isCoHost || !props.isHost)
                ? "disabled"
                : ""
            }`}
          >
            <TrackToggle
              source={Track.Source.Camera}
              showIcon={showIcon}
              onChange={cameraOnChange}
              className="control-bar-button-camera control-bar-button control-bar-button-icon"
              onDeviceError={onDeviceError}
            >
              {showText && "Camera"}
            </TrackToggle>
            {!isMobileBrowser() && (
              <div className="lk-button-group-menu">
                <MediaDeviceMenu
                  kind="videoinput"
                  onActiveDeviceChange={(_kind, deviceId) =>
                    saveVideoInputDeviceId(deviceId ?? "")
                  }
                />
              </div>
            )}
          </div>
        </Popover>
      )}
      {/* {visibleControls.camera && isMobileBrowser() && (
        <Popover
          content={"Select Camera"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <div className="lk-button-group">
            <div>
              <MediaDeviceMenu
                kind="videoinput"
                onActiveDeviceChange={(_kind, deviceId) =>
                  saveVideoInputDeviceId(deviceId ?? "")
                }
              >
                <div className="camera-select-icon">
                  <svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" fill="currentColor"/>
                    <path d="M20 4H16.83L15 2H9L7.17 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17Z" fill="currentColor"/>
                  </svg>
                </div>
              </MediaDeviceMenu>
            </div>
          </div>
        </Popover>
      )} */}
      {/* Chat button */}
      {meetingFeatures?.conference_chat === 1 && visibleControls.chat && (
        <Popover
          content={"Chats"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <Badge
            count={
              privateChatUnreadMessagesCount + publicChatUnreadMessagesCount
            }
            overflowCount={9}
            color="blue"
          >
            <ChatControlButton
              showChatDrawer={showChatDrawer}
              setShowChatDrawer={setShowChatDrawer}
              setDrawerState={setDrawerState}
            />
          </Badge>
        </Popover>
      )}
      {/* {meetingFeatures?.conference_chat === 1 && visibleControls.chat && (
        <ChatToggle className="control-bar-button-icon control-bar-button">
          {showIcon && <ChatIcon />}
          {showText && "Chat"}
        </ChatToggle>
      )} */}

      {!isMobileBrowser() ? (
        <Popover
          content={"Reactions"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <div className="control-bar-button-reactions">
            <ReactionsControlButton
              showEmojiReaction={showEmojiReaction}
              setShowEmojiReaction={setShowEmojiReaction}
            />
          </div>
        </Popover>
      ) : null}
      {!isMobileBrowser() &&
      isWebinarMode &&
      (props.isHost || props.isCoHost) ? (
        <Popover
          content={"Share Screen"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <>
            {meetingFeatures?.screen_sharing === 1 &&
              visibleControls.screenShare &&
              browserSupportsScreenSharing &&
              (isScreenShareEnabled ? (
                <TrackToggle
                  source={Track.Source.ScreenShare}
                  captureOptions={
                    screenShareMode === "text"
                      ? {
                          audio: true,
                          contentHint: "detail",
                          resolution: {
                            width: props.maxWidth,
                            height: props.maxHeight,
                            frameRate: 5,
                          },
                        }
                      : {
                          audio: true,
                          contentHint: "detail",
                          resolution: {
                            width: props.maxWidth,
                            height: props.maxHeight,
                            frameRate: 30,
                          },
                        }
                  }
                  showIcon={showIcon}
                  onChange={onScreenShareChange}
                  onClick={() => setIsPIPEnabled(false)}
                  onDeviceError={onDeviceError}
                  className="control-bar-button control-bar-button-icon"
                />
              ) : (
                <ScreenShareMenuButton
                  onScreenShareChange={onScreenShareChange}
                  maxWidth={props.maxWidth}
                  maxHeight={props.maxHeight}
                  roomData={props.room}
                  setScreenShareMode={setScreenShareMode}
                  meetingFeatures={meetingFeatures}
                  setIsPIPEnabled={setIsPIPEnabled}
                  screenShareSources={screenShareSources}
                  isElectronApp={isElectronApp}
                  room={props.room}
                  setScreenShareDisplayId={setScreenShareDisplayId}
                  setToastNotification={setToastNotification}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                />
              ))}
          </>
        </Popover>
      ) : !isMobileBrowser() && !isWebinarMode ? (
        <Popover
          content={"Share Screen"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <>
            {meetingFeatures?.screen_sharing === 1 &&
              visibleControls.screenShare &&
              browserSupportsScreenSharing &&
              (isScreenShareEnabled ? (
                <TrackToggle
                  source={Track.Source.ScreenShare}
                  captureOptions={
                    screenShareMode === "text"
                      ? {
                          audio: true,
                          contentHint: "detail",
                          resolution: {
                            width: props.maxWidth,
                            height: props.maxHeight,
                            frameRate: 5,
                          },
                        }
                      : {
                          audio: true,
                          contentHint: "detail",
                          resolution: {
                            width: props.maxWidth,
                            height: props.maxHeight,
                            frameRate: 30,
                          },
                        }
                  }
                  showIcon={showIcon}
                  onChange={onScreenShareChange}
                  onClick={() => setIsPIPEnabled(false)}
                  onDeviceError={onDeviceError}
                  className="control-bar-button control-bar-button-icon"
                />
              ) : (
                <ScreenShareMenuButton
                  onScreenShareChange={onScreenShareChange}
                  maxWidth={props.maxWidth}
                  maxHeight={props.maxHeight}
                  roomData={props.room}
                  setScreenShareMode={setScreenShareMode}
                  meetingFeatures={meetingFeatures}
                  setIsPIPEnabled={setIsPIPEnabled}
                  screenShareSources={screenShareSources}
                  isElectronApp={isElectronApp}
                  room={props.room}
                  setScreenShareDisplayId={setScreenShareDisplayId}
                  setToastNotification={setToastNotification}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                />
              ))}
          </>
        </Popover>
      ) : null}
      {/* {isScreenShareEnabled && isElectronApp && (
        <AnnotationControlButton
          isElectronApp={isElectronApp}
          screenShareFocus={isScreenShareEnabled}
          isAnnotationEnabled={isAnnotationEnabled}
          setIsAnnotationEnabled={setIsAnnotationEnabled}
        />
      )} */}
      {meetingFeatures?.raise_hand === 1 && !isMobileBrowser() && (
        <Popover
          content={"Raise Hand"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <>
            <RaiseHandControlButton
              showRaiseHand={showRaiseHand}
              setShowRaiseHand={setShowRaiseHand}
            />
          </>
        </Popover>
      )}
      {!isMobileBrowser() &&
      isWebinarMode &&
      (props.isHost || props.isCoHost) ? (
        <Popover
          content={"Participants"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <Badge
            count={remoteParticipantsArray.length}
            color="transparent"
            style={{ boxShadow: "none" }}
          >
            <ParticipantControlButton
              showParticipantsList={showParticipantsList}
              setShowParticipantsList={setShowParticipantsList}
              setDrawerState={setDrawerState}
            />
          </Badge>
        </Popover>
      ) : !isMobileBrowser() && !isWebinarMode ? (
        <Popover
          content={"Participants"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <>
            <Badge
              count={remoteParticipantsArray.length}
              color="transparent"
              style={{ boxShadow: "none" }}
            >
              <ParticipantControlButton
                showParticipantsList={showParticipantsList}
                setShowParticipantsList={setShowParticipantsList}
                setDrawerState={setDrawerState}
              />
            </Badge>
          </>
        </Popover>
      ) : null}
      {/* {!isMobileBrowser() ? (
        <> */}
      {visibleControls.settings && (
        <div className="control-bar-more-options">
          <span>More Options</span>
          <SettingsControlButton
            id={props.id}
            isHost={props.isHost}
            meetingDetails={props.meetingDetails}
            setIsVBDrawerOpen={props.setIsVBDrawerOpen}
            isVBDrawerOpen={props.isVBDrawerOpen}
            setIsRPDrawerOpen={props.setIsRPDrawerOpen}
            isRPDrawerOpen={props.isRPDrawerOpen}
            isLiveCaptionsDrawerOpen={props.isLiveCaptionsDrawerOpen}
            setIsLiveCaptionsDrawerOpen={props.setIsLiveCaptionsDrawerOpen}
            setDrawerState={setDrawerState}
            setShowRecording={props.setShowRecording}
            isCoHost={props.isCoHost}
            showParticipantsList={showParticipantsList}
            setShowParticipantsList={setShowParticipantsList}
            showBreakoutRoom={showBreakoutRoom}
            setShowBreakoutRoom={setShowBreakoutRoom}
            showRaiseHand={showRaiseHand}
            setShowRaiseHand={setShowRaiseHand}
            showHostControl={showHostControl}
            setShowHostControl={setShowHostControl}
            coHostToken={coHostToken}
            showRecording={showRecording}
            room={props.room}
            isBreakoutRoom={isBreakoutRoom}
            meetingFeatures={meetingFeatures}
            showlivecaptionsicon={showlivecaptionsicon}
            isPIPEnabled={isPIPEnabled}
            setIsPIPEnabled={setIsPIPEnabled}
            isWhiteboardOpen={props.isWhiteboardOpen}
            setIsWhiteboardOpen={props.setIsWhiteboardOpen}
            isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
            setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
            whiteboardSceneData={whiteboardSceneData}
            setWhiteboardSceneData={setWhiteboardSceneData}
            whiteBoardId={whiteBoardId}
            setWhiteboardId={setWhiteboardId}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            setToastNotification={setToastNotification}
            setToastStatus={setToastStatus}
            setShowToast={setShowToast}
            isRecordingLoading={isRecordingLoading}
            setIsRecordingLoading={setIsRecordingLoading}
            setParticipantConsent={setParticipantConsent}
            setShowRecordingConsentIcon={setShowRecordingConsentIcon}
            remoteParticipants={remoteParticipants}
            brightness={brightness}
            onBrightnessChange={onBrightnessChange}
            outputVolume={outputVolume}
            onOutputVolumeChange={onOutputVolumeChange}
            autoVideoOff={autoVideoOff}
            onAutoVideoOffChange={onAutoVideoOffChange}
            autoAudioOff={autoAudioOff}
            onAutoAudioOffChange={onAutoAudioOffChange}
          />
        </div>
      )}

      {visibleControls.leave &&
        (props.isHost || props.isCoHost ? (
          <div className="control-bar-more-options">
            <span>Disconnect</span>
            <DisconnectButtonMenu
              id={props.id}
              setShowPopover={setShowDisconnectPopover}
              showPopover={showDisconnectPopover}
              coHostToken={coHostToken}
              isElectronApp={isElectronApp}
              setToastNotification={setToastNotification}
              setToastStatus={setToastStatus}
              setShowToast={setShowToast}
              // isAnnotationEnabled={isAnnotationEnabled}
            />
          </div>
        ) : (
          <div className="control-bar-more-options">
            <span>Disconnect</span>
            <DisconnectButton
              className="control-bar-button control-bar-button-icon"
              style={{ backgroundColor: "rgba(255, 59, 48, 1)" }}
              onClick={() => {
                if (isElectronApp) {
                  window?.electronAPI?.ipcRenderer?.send("stop-annotation");
                }
              }}
            >
              {showIcon && <LeaveIcon />}
              {showText && "Leave"}
            </DisconnectButton>
          </div>
        ))}
      <StartMediaButton />
    </div>
  );
}

